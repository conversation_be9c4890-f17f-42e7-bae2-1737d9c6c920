'use client';

import React from 'react';
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON> } from 'lucide-react';

export const AdvancedFeatures = () => {
  return (
    <div className="w-full py-20 lg:py-32 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-12 lg:gap-20 items-center">
          {/* Left side - Advanced Editor Mockup */}
          <div className="flex-1 max-w-2xl order-2 lg:order-1">
            <div className="relative">
              {/* Advanced Editor Window */}
              <div className="bg-[#0d1117] rounded-lg overflow-hidden shadow-2xl border border-gray-800">
                {/* Title Bar */}
                <div className="bg-[#161b22] px-4 py-3 flex items-center justify-between border-b border-gray-700">
                  <div className="flex items-center gap-2">
                    <div className="flex gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-gray-300 text-sm ml-4">AI Assistant</span>
                  </div>
                  <div className="text-gray-400 text-xs">Enterprise ready</div>
                </div>

                {/* Editor Content */}
                <div className="p-6 font-mono text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">1</span>
                      <span className="text-purple-400">// AI-powered code analysis</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">2</span>
                      <span className="text-blue-400">class</span>
                      <span className="text-yellow-400">SmartAnalyzer</span>
                      <span className="text-white">{</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">3</span>
                      <span className="text-white ml-4">async</span>
                      <span className="text-blue-400">analyzeCode</span>
                      <span className="text-white">(</span>
                      <span className="text-orange-400">codebase</span>
                      <span className="text-white">) {</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">4</span>
                      <span className="text-white ml-8">const insights = await</span>
                      <span className="text-green-400">AI</span>
                      <span className="text-white">.analyze({</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">5</span>
                      <span className="text-white ml-12">context:</span>
                      <span className="text-green-400">'deep'</span>
                      <span className="text-white">,</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">6</span>
                      <span className="text-white ml-12">patterns:</span>
                      <span className="text-green-400">'architectural'</span>
                      <span className="text-white">,</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">7</span>
                      <span className="text-white ml-12">security:</span>
                      <span className="text-blue-400">true</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">8</span>
                      <span className="text-white ml-8">});</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">9</span>
                      <span className="text-white ml-8">return insights;</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">10</span>
                      <span className="text-white ml-4">}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-600 w-6 text-right">11</span>
                      <span className="text-white">}</span>
                    </div>
                  </div>
                </div>

                {/* Status Bar */}
                <div className="bg-[#21262d] px-4 py-2 flex items-center justify-between text-xs border-t border-gray-700">
                  <div className="flex items-center gap-4 text-gray-300">
                    <span>🧠 AI Active</span>
                    <span>⚡ Fast Mode</span>
                    <span>🔒 Secure</span>
                  </div>
                  <span className="text-gray-300">Enterprise ready</span>
                </div>
              </div>

              {/* Floating Icons */}
              <div className="absolute -top-4 -left-4 w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center shadow-lg">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -bottom-4 -right-4 w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <div className="absolute top-1/2 -left-6 w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center shadow-lg">
                <Shield className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>

          {/* Right side - Content */}
          <div className="flex-1 max-w-lg order-1 lg:order-2">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-purple-400 text-sm font-medium tracking-wide uppercase">
                Advanced AI
              </span>
            </div>
            
            <h2 className="text-4xl lg:text-5xl font-regular tracking-tighter text-white mb-6 leading-tight">
              Enterprise-grade AI development platform
            </h2>
            
            <p className="text-lg text-gray-300 leading-relaxed mb-8">
              Experience the next generation of AI-powered development with advanced security, deep code analysis, and intelligent automation. Built for teams that demand the highest standards of code quality and security.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-1 h-6 bg-purple-500 rounded-full"></div>
                <span className="text-white font-medium">Deep architectural analysis</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-1 h-6 bg-purple-500 rounded-full"></div>
                <span className="text-white font-medium">Enterprise security standards</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-1 h-6 bg-purple-500 rounded-full"></div>
                <span className="text-white font-medium">Intelligent code optimization</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedFeatures;
