@import "tailwindcss";
@import "@repo/design-system/styles/globals.css";

/* Hide Clerk internal elements for cleaner authentication UI */
.cl-footer,
.cl-footerAction,
.cl-footerActionLink,
.cl-footerActionText,
.cl-footerPages,
.cl-footerPagesLink,
.cl-header,
.cl-headerTitle,
.cl-headerSubtitle,
.cl-alternativeMethodsBlockButton,
.cl-identityPreview,
.cl-footerActionLinkText,
.cl-footerActionLinkPages,
.cl-footerActionLinkPagesText {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Hide elements with specific data attributes */
[data-localization-key="signIn.alternativeMethodsBlockButton"],
[data-localization-key="footerActionLink__signUp"],
[data-localization-key="footerPages__privacy"],
[data-localization-key="footerPages__terms"],
[data-localization-key="footerActionText"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Ensure modal background is fully opaque white */
.cl-rootBox,
.cl-card,
.cl-main,
.cl-modalContent {
  background-color: #ffffff !important;
  background: #ffffff !important;
}

/* Fix any transparent or semi-transparent backgrounds */
.cl-rootBox * {
  background-color: transparent;
}

.cl-rootBox,
.cl-card {
  background-color: #ffffff !important;
}
