@import "tailwindcss";
@import "@repo/design-system/styles/globals.css";

/* Hide only specific Clerk internal elements - keep buttons and sign up links */
.cl-header,
.cl-headerTitle,
.cl-headerSubtitle,
.cl-identityPreview,
.cl-alternativeMethodsBlockButton {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Hide only privacy/terms links, not the sign up link */
[data-localization-key="footerPages__privacy"],
[data-localization-key="footerPages__terms"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Ensure modal background is fully opaque white */
.cl-rootBox,
.cl-card,
.cl-main,
.cl-modalContent {
  background-color: #ffffff !important;
  background: #ffffff !important;
}

/* Fix any transparent or semi-transparent backgrounds */
.cl-rootBox * {
  background-color: transparent;
}

.cl-rootBox,
.cl-card {
  background-color: #ffffff !important;
}
