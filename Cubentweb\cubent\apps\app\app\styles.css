@import "tailwindcss";
@import "@repo/design-system/styles/globals.css";

/* Hide only specific Clerk internal elements - keep buttons and sign up links */
.cl-header,
.cl-headerTitle,
.cl-headerSubtitle,
.cl-identityPreview,
.cl-alternativeMethodsBlockButton {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Hide only privacy/terms links, not the sign up link */
[data-localization-key="footerPages__privacy"],
[data-localization-key="footerPages__terms"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Ensure modal background is fully opaque white and remove all borders/shadows */
.cl-rootBox,
.cl-card,
.cl-main,
.cl-modalContent {
  background-color: #ffffff !important;
  background: #ffffff !important;
  border: none !important;
  box-shadow: none !important;
}

/* Remove internal borders and shadows from all child elements */
.cl-rootBox *,
.cl-card *,
.cl-main * {
  border: none !important;
  box-shadow: none !important;
}

/* Fix any transparent or semi-transparent backgrounds */
.cl-rootBox * {
  background-color: transparent;
}

.cl-rootBox,
.cl-card {
  background-color: #ffffff !important;
  border: none !important;
  box-shadow: none !important;
}

/* Force Continue button to be orange with reduced height and NO ROUNDING */
.cl-formButtonPrimary,
button[data-localization-key*="formButtonPrimary"],
button[type="submit"] {
  background: linear-gradient(to right, #f97316, #ea580c) !important;
  background-color: #f97316 !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s !important;
  padding: 6px 24px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  width: 100% !important;
  border-radius: 0px !important;
}

.cl-formButtonPrimary:hover,
button[data-localization-key*="formButtonPrimary"]:hover,
button[type="submit"]:hover {
  background: linear-gradient(to right, #ea580c, #dc2626) !important;
  background-color: #ea580c !important;
  transform: translateY(-1px) !important;
}

/* Style Google button with background - MORE SPECIFIC SELECTORS */
.cl-socialButtonsBlockButton,
button[data-localization-key*="socialButtonsBlockButton"],
button[data-localization-key*="Continue with Google"],
.cl-socialButtons button,
.cl-socialButtonsProviderIcon__google {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  color: #374151 !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

/* Ensure Google icon is visible */
.cl-socialButtonsProviderIcon,
.cl-socialButtonsProviderIcon__google,
.cl-socialButtonsBlockButton svg,
.cl-socialButtons button svg {
  display: inline-block !important;
  width: 20px !important;
  height: 20px !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.cl-socialButtonsBlockButton:hover,
button[data-localization-key*="socialButtonsBlockButton"]:hover,
button[data-localization-key*="Continue with Google"]:hover,
.cl-socialButtons button:hover {
  background-color: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Style email input field with grey background - MORE SPECIFIC SELECTORS */
.cl-formFieldInput,
input[type="email"],
input[name="identifier"],
input[placeholder*="email"],
input[placeholder*="Enter your email"],
.cl-field input,
.cl-formField input,
.cl-formFieldInput__identifier {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 16px !important;
  transition: all 0.2s !important;
  color: #374151 !important;
}

.cl-formFieldInput:focus,
input[type="email"]:focus,
input[name="identifier"]:focus,
input[placeholder*="email"]:focus,
input[placeholder*="Enter your email"]:focus,
.cl-field input:focus,
.cl-formField input:focus,
.cl-formFieldInput__identifier:focus {
  background-color: #ffffff !important;
  border-color: #f97316 !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
  outline: none !important;
}

/* AGGRESSIVE SELECTORS - Force grey backgrounds on ALL possible elements */
[data-testid*="email"] input,
[data-testid*="identifier"] input,
div[class*="formField"] input,
div[class*="field"] input,
input[autocomplete="email"],
input[type="text"][name*="email"],
input[type="text"][name*="identifier"] {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
}

/* Force Google button styling */
button[class*="social"],
button[class*="provider"],
div[class*="social"] button,
div[class*="provider"] button,
button:has(svg[class*="google"]),
button:has([data-testid*="google"]) {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
}

/* Reduce spacing between elements - make them closer together */
.cl-main,
.cl-card,
.cl-rootBox {
  gap: 12px !important;
}

.cl-formFieldRow,
.cl-field,
.cl-formField {
  margin-bottom: 8px !important;
}

.cl-socialButtonsBlockButton {
  margin-bottom: 12px !important;
}

.cl-dividerRow {
  margin: 8px 0 !important;
}

/* Reduce internal spacing */
.cl-internal {
  padding: 0 !important;
  margin: 0 !important;
}

/* Make "or" divider closer */
.cl-dividerText {
  margin: 4px 0 !important;
}
