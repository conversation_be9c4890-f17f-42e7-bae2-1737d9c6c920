@import "tailwindcss";
@import "@repo/design-system/styles/globals.css";

/* Hide only specific Clerk internal elements - keep buttons and sign up links */
.cl-header,
.cl-headerTitle,
.cl-headerSubtitle,
.cl-identityPreview,
.cl-alternativeMethodsBlockButton {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Hide only privacy/terms links, not the sign up link */
[data-localization-key="footerPages__privacy"],
[data-localization-key="footerPages__terms"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Ensure modal background is fully opaque white and remove all borders/shadows */
.cl-rootBox,
.cl-card,
.cl-main,
.cl-modalContent {
  background-color: #ffffff !important;
  background: #ffffff !important;
  border: none !important;
  box-shadow: none !important;
}

/* Remove internal borders and shadows from all child elements */
.cl-rootBox *,
.cl-card *,
.cl-main * {
  border: none !important;
  box-shadow: none !important;
}

/* Fix any transparent or semi-transparent backgrounds */
.cl-rootBox * {
  background-color: transparent;
}

.cl-rootBox,
.cl-card {
  background-color: #ffffff !important;
  border: none !important;
  box-shadow: none !important;
}

/* Force Continue button to be orange with reduced height and NO ROUNDING */
.cl-formButtonPrimary,
button[data-localization-key*="formButtonPrimary"],
button[type="submit"] {
  background: linear-gradient(to right, #f97316, #ea580c) !important;
  background-color: #f97316 !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s !important;
  padding: 6px 24px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  width: 100% !important;
  border-radius: 0px !important;
}

.cl-formButtonPrimary:hover,
button[data-localization-key*="formButtonPrimary"]:hover,
button[type="submit"]:hover {
  background: linear-gradient(to right, #ea580c, #dc2626) !important;
  background-color: #ea580c !important;
  transform: translateY(-1px) !important;
}

/* Style Google button with background - MORE SPECIFIC SELECTORS */
.cl-socialButtonsBlockButton,
button[data-localization-key*="socialButtonsBlockButton"],
button[data-localization-key*="Continue with Google"],
.cl-socialButtons button,
.cl-socialButtonsProviderIcon__google {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  color: #374151 !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

/* Ensure Google icon is visible - FORCE IT */
.cl-socialButtonsProviderIcon,
.cl-socialButtonsProviderIcon__google,
.cl-socialButtonsBlockButton svg,
.cl-socialButtons button svg,
.cl-socialButtonsBlockButton::before {
  display: inline-block !important;
  width: 20px !important;
  height: 20px !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* ADD GOOGLE ICON MANUALLY if it's missing */
.cl-socialButtonsBlockButton::before,
button[data-localization-key*="socialButtonsBlockButton"]::before,
button[data-localization-key*="Continue with Google"]::before {
  content: "" !important;
  display: inline-block !important;
  width: 20px !important;
  height: 20px !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  margin-right: 8px !important;
}

.cl-socialButtonsBlockButton:hover,
button[data-localization-key*="socialButtonsBlockButton"]:hover,
button[data-localization-key*="Continue with Google"]:hover,
.cl-socialButtons button:hover {
  background-color: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Style email input field with grey background - MORE SPECIFIC SELECTORS */
.cl-formFieldInput,
input[type="email"],
input[name="identifier"],
input[placeholder*="email"],
input[placeholder*="Enter your email"],
.cl-field input,
.cl-formField input,
.cl-formFieldInput__identifier {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 16px !important;
  transition: all 0.2s !important;
  color: #374151 !important;
}

.cl-formFieldInput:focus,
input[type="email"]:focus,
input[name="identifier"]:focus,
input[placeholder*="email"]:focus,
input[placeholder*="Enter your email"]:focus,
.cl-field input:focus,
.cl-formField input:focus,
.cl-formFieldInput__identifier:focus {
  background-color: #ffffff !important;
  border-color: #f97316 !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
  outline: none !important;
}

/* AGGRESSIVE SELECTORS - Force grey backgrounds on ALL possible elements */
[data-testid*="email"] input,
[data-testid*="identifier"] input,
div[class*="formField"] input,
div[class*="field"] input,
input[autocomplete="email"],
input[type="text"][name*="email"],
input[type="text"][name*="identifier"] {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
}

/* Force Google button styling */
button[class*="social"],
button[class*="provider"],
div[class*="social"] button,
div[class*="provider"] button,
button:has(svg[class*="google"]),
button:has([data-testid*="google"]) {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
}

/* Reduce spacing between elements - make them closer together */
.cl-main,
.cl-card,
.cl-rootBox {
  gap: 12px !important;
}

.cl-formFieldRow,
.cl-field,
.cl-formField {
  margin-bottom: 8px !important;
}

.cl-socialButtonsBlockButton {
  margin-bottom: 12px !important;
}

.cl-dividerRow {
  margin: 8px 0 !important;
}

/* Reduce internal spacing */
.cl-internal {
  padding: 0 !important;
  margin: 0 !important;
}

/* Make "or" divider closer */
.cl-dividerText {
  margin: 4px 0 !important;
}
