'use client';

import React from 'react';
import { Code2, Database, Terminal } from 'lucide-react';

export const ExtensionTools = () => {
  return (
    <div className="w-full py-20 lg:py-32 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-12 lg:gap-20 items-center">
          {/* Left side - Content */}
          <div className="flex-1 max-w-lg">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span className="text-orange-400 text-sm font-medium tracking-wide uppercase">
                Extension Tools
              </span>
            </div>
            
            <h2 className="text-4xl lg:text-5xl font-regular tracking-tighter text-white mb-6 leading-tight">
              Powerful MCP tools for modern development
            </h2>
            
            <p className="text-lg text-gray-300 leading-relaxed mb-8">
              Built on the same framework as VS Code, we've made the best possible multiplayer development environment for teams who want to write their own queries, collaborate in real-time, and maintain best-in-class code completion.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-1 h-6 bg-orange-500 rounded-full"></div>
                <span className="text-white font-medium">Real-time collaboration</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-1 h-6 bg-orange-500 rounded-full"></div>
                <span className="text-white font-medium">Advanced code completion</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-1 h-6 bg-orange-500 rounded-full"></div>
                <span className="text-white font-medium">Integrated debugging tools</span>
              </div>
            </div>
          </div>

          {/* Right side - Code Editor Mockup */}
          <div className="flex-1 max-w-2xl">
            <div className="relative">
              {/* Editor Window */}
              <div className="bg-[#1e1e1e] rounded-lg overflow-hidden shadow-2xl border border-gray-700">
                {/* Title Bar */}
                <div className="bg-[#2d2d30] px-4 py-3 flex items-center justify-between border-b border-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="flex gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-gray-300 text-sm ml-4">MCP Tools</span>
                  </div>
                  <div className="text-gray-400 text-xs">Professional grade</div>
                </div>

                {/* Editor Content */}
                <div className="p-6 font-mono text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">1</span>
                      <span className="text-blue-400">import</span>
                      <span className="text-white">{ MCP } from</span>
                      <span className="text-green-400">'@cubent/mcp-tools'</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">2</span>
                      <span className="text-blue-400">import</span>
                      <span className="text-white">{ Database, Terminal } from</span>
                      <span className="text-green-400">'@cubent/core'</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">3</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">4</span>
                      <span className="text-purple-400">const</span>
                      <span className="text-white">tools = new</span>
                      <span className="text-yellow-400">MCP</span>
                      <span className="text-white">({</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">5</span>
                      <span className="text-white ml-4">database:</span>
                      <span className="text-blue-400">Database</span>
                      <span className="text-white">.connect(),</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">6</span>
                      <span className="text-white ml-4">terminal:</span>
                      <span className="text-blue-400">Terminal</span>
                      <span className="text-white">.create(),</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">7</span>
                      <span className="text-white ml-4">features: [</span>
                      <span className="text-green-400">'autocomplete'</span>
                      <span className="text-white">,</span>
                      <span className="text-green-400">'debugging'</span>
                      <span className="text-white">]</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-gray-500 w-6 text-right">8</span>
                      <span className="text-white">});</span>
                    </div>
                  </div>
                </div>

                {/* Status Bar */}
                <div className="bg-[#007acc] px-4 py-2 flex items-center justify-between text-xs">
                  <div className="flex items-center gap-4 text-white">
                    <span>60 FPS</span>
                    <span>70MB RAM</span>
                    <span>44°C CPU</span>
                    <span>10% CPU</span>
                  </div>
                  <span className="text-white">Professional grade</span>
                </div>
              </div>

              {/* Floating Icons */}
              <div className="absolute -top-4 -right-4 w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center shadow-lg">
                <Code2 className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
                <Database className="w-5 h-5 text-white" />
              </div>
              <div className="absolute top-1/2 -right-6 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                <Terminal className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtensionTools;
