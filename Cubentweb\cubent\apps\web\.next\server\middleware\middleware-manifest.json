{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_65232333._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_5738550d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|images|ingest|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "42e6f869adb749f5333414621ec805d5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e2d39b6ddffe68c0c04581e4aa07fa586fc0bcd94bfdda9ae82babba5417ae6d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5cba9cef82251572fa2ea2e14146e7e824b6220a224fca18f0173eb1957b8583"}}}, "instrumentation": null, "functions": {}}